<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMembersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('members')) {
            Schema::create('members', function (Blueprint $table) {
                $table->string('id', 32)->primary();
                $table->softDeletes();
                $table->timestamps();
                $table->string('email', 200);
                $table->string('first_name');
                $table->string('middle_names')->nullable();
                $table->string('last_name');
                $table->string('dob', 50)->nullable();
                $table->string('city', 200)->nullable();
                $table->string('country', 200)->nullable();
                $table->string('street', 200)->nullable();
                $table->string('zip', 100)->nullable();
                $table->string('gender', 10)->nullable();
                $table->string('phone', 30)->nullable();
                $table->string('nok_city', 200)->nullable();
                $table->string('nok_country', 200)->nullable();
                $table->string('nok_dob', 50)->nullable();
                $table->string('nok_email', 200)->nullable();
                $table->string('nok_full_name', 200)->nullable();
                $table->string('nok_phone', 30)->nullable();
                $table->string('nok_street', 200)->nullable();
                $table->string('nok_zip', 50)->nullable();
                $table->string('orderID', 64)->nullable();
                $table->integer('user_id')->unique();
                $table->text('bio')->nullable();
                $table->string('balance')->default('0')->nullable();
                $table->integer('misc')->nullable();
                $table->boolean('deny_email')->nullable();
                $table->text('notes')->nullable();
                $table->text('nominee_update')->nullable();
                $table->integer('affiliate_id')->nullable();
                $table->string('referred_by')->nullable();
                
                // Indexes
                $table->index('email');
                $table->index('affiliate_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('members');
    }
}
