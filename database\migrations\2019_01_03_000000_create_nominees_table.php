<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNomineesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('nominees')) {
            Schema::create('nominees', function (Blueprint $table) {
                $table->increments('id');
                $table->string('full_name', 200)->nullable();
                $table->date('dob')->nullable();
                $table->string('zimbabwean_by', 100)->nullable();
                $table->timestamps();
                $table->softDeletes();
                $table->string('email', 200)->nullable();
                $table->string('member_id');
                $table->string('supportingdoc1')->nullable();
                $table->string('supportingdoc2')->nullable();
                $table->string('supportingdoc3')->nullable();
                $table->string('phone')->nullable();
                
                // Foreign key constraint
                $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
                
                // Indexes
                $table->index('member_id');
                $table->index('email');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('nominees');
    }
}
