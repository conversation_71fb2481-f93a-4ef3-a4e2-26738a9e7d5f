<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('invoices')) {
            Schema::create('invoices', function (Blueprint $table) {
                $table->increments('id');
                $table->integer('user_id')->nullable();
                $table->string('status')->nullable();
                $table->double('subtotal')->nullable();
                $table->double('total')->nullable();
                $table->string('type')->nullable();
                $table->timestamps();
                $table->string('member_id')->nullable();
                $table->softDeletes();
                $table->string('reminder')->nullable();
                $table->string('description')->nullable();
                $table->integer('obituary_id')->nullable();
                $table->datetime('invoice_date')->nullable();
                $table->datetime('due_date')->nullable();
                $table->string('poll_url')->nullable();
                $table->integer('applicant_id')->nullable();
                
                // Foreign key constraints
                $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
                $table->foreign('obituary_id')->references('id')->on('obituaries')->onDelete('set null');
                
                // Indexes
                $table->index('member_id');
                $table->index('obituary_id');
                $table->index('status');
                $table->index('type');
                $table->index(['created_at', 'status']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoices');
    }
}
