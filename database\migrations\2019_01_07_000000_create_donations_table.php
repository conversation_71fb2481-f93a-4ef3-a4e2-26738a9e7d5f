<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDonationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('donations')) {
            Schema::create('donations', function (Blueprint $table) {
                $table->increments('id');
                $table->integer('obituary_id')->nullable();
                $table->string('amount', 30)->nullable();
                $table->datetime('on')->nullable();
                $table->string('orderID', 50)->nullable();
                $table->softDeletes();
                $table->timestamps();
                $table->string('description')->nullable();
                $table->integer('invoice_id')->nullable();
                $table->string('member_id');
                $table->string('payment_ref')->nullable();
                
                // Foreign key constraints
                $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
                $table->foreign('obituary_id')->references('id')->on('obituaries')->onDelete('set null');
                $table->foreign('invoice_id')->references('id')->on('invoices')->onDelete('set null');
                
                // Indexes
                $table->index('member_id');
                $table->index('obituary_id');
                $table->index('invoice_id');
                $table->index('on');
                $table->index(['member_id', 'obituary_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('donations');
    }
}
