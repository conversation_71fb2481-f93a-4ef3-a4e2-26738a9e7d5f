<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMemberServiceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('member_service')) {
            Schema::create('member_service', function (Blueprint $table) {
                $table->increments('id');
                $table->string('member_id');
                $table->integer('service_id');
                $table->decimal('price_paid', 10, 2)->nullable();
                $table->datetime('purchased_at')->nullable();
                $table->string('status')->default('active'); // active, expired, cancelled
                $table->timestamps();
                
                // Foreign key constraints
                $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
                $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
                
                // Indexes
                $table->index('member_id');
                $table->index('service_id');
                $table->index(['status', 'purchased_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_service');
    }
}
