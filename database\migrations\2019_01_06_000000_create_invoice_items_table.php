<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoiceItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('invoice_items')) {
            Schema::create('invoice_items', function (Blueprint $table) {
                $table->increments('id');
                $table->string('type')->nullable();
                $table->string('description')->nullable();
                $table->string('title')->nullable();
                $table->double('amount')->nullable();
                $table->integer('units')->nullable();
                $table->double('unit_price')->nullable();
                $table->double('discount_percent')->nullable();
                $table->timestamps();
                $table->integer('invoice_id');
                $table->softDeletes();
                
                // Foreign key constraint
                $table->foreign('invoice_id')->references('id')->on('invoices')->onDelete('cascade');
                
                // Indexes
                $table->index('invoice_id');
                $table->index('type');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice_items');
    }
}
