<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMemberObituaryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('member_obituary')) {
            Schema::create('member_obituary', function (Blueprint $table) {
                $table->increments('id');
                $table->string('member_id');
                $table->integer('obituary_id');
                $table->timestamps();
                
                // Foreign key constraints
                $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
                $table->foreign('obituary_id')->references('id')->on('obituaries')->onDelete('cascade');
                
                // Unique constraint to prevent duplicates
                $table->unique(['member_id', 'obituary_id']);
                
                // Indexes
                $table->index('member_id');
                $table->index('obituary_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_obituary');
    }
}
