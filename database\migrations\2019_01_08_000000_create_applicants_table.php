<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApplicantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('applicants')) {
            Schema::create('applicants', function (Blueprint $table) {
                $table->increments('id');
                $table->string('first_name')->nullable();
                $table->string('middle_names')->nullable();
                $table->string('last_name')->nullable();
                $table->string('dob', 100)->nullable();
                $table->string('email', 200)->nullable();
                $table->string('street', 200)->nullable();
                $table->string('city', 100)->nullable();
                $table->string('country', 100)->nullable();
                $table->string('zip', 100)->nullable();
                $table->string('gender', 2)->nullable();
                $table->string('phone', 32)->nullable();
                $table->string('nok_city', 100)->nullable();
                $table->string('nok_country', 100)->nullable();
                $table->string('nok_dob', 100)->nullable();
                $table->string('nok_email', 200)->nullable();
                $table->string('nok_full_name')->nullable();
                $table->string('nok_phone', 32)->nullable();
                $table->string('nok_street')->nullable();
                $table->string('nok_zip', 100)->nullable();
                $table->text('nominees')->nullable();
                $table->integer('read_constitution')->nullable();
                $table->integer('certify_details')->nullable();
                $table->integer('uk_resident')->nullable();
                $table->timestamps();
                $table->softDeletes();
                $table->string('status', 32)->default('pending')->nullable();
                $table->string('token', 64)->nullable();
                $table->string('apartment')->nullable();
                $table->string('nok_apartment')->nullable();
                $table->boolean('welcome_send')->default(0)->nullable();
                $table->string('paymentref', 50)->nullable();
                $table->integer('affiliate_id')->nullable();
                $table->string('referred_by')->nullable();
                
                // Foreign key constraint
                $table->foreign('affiliate_id')->references('id')->on('affiliates')->onDelete('set null');
                
                // Indexes
                $table->index('email');
                $table->index('status');
                $table->index('affiliate_id');
                $table->index('token');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applicants');
    }
}
