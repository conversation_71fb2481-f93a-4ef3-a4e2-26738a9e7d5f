<?php

namespace App\Http\Controllers;

use App\User;
use Exception;
use App\Models\Member;
use App\Models\Deposit;
use App\Models\Applicant;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Notifications\DepositNotification;
use Illuminate\Support\Facades\Notification;

class DepositController extends SharedBaseController
{
    public function store(Request $request)
    {

        // dd($request);


        if(isset($request->applicant_id)){
                    $applicant = Applicant::findOrFail($request->applicant_id);

            $applicantService = new ApplicantController();

              $applicantService->joiningfee($applicant, $request);
            return redirect(to: route('forgot.password'));

        }

        if(!isset($request->payment_ref)){
            logger("Payment reference not set");
            return back()->with([
                'message'    => "Failed to validate your payment. Please contact support",
                'alert-type' => 'danger',
            ]);
        }

        $depositCheck = Deposit::wherePaymentRef($request->payment_ref)->get();
        if(count($depositCheck)!=0){
            throw new Exception("Payment already processed");
            return back()->with([
                'message'    => "Payment arlready processed",
                'alert-type' => 'danger',
            ]);
        }

        $amount = getAmount($request->payment_ref);

        // dd($amount);

        $user = User::find(Auth::user()->id);
        $member = $user->memberDetails;
        if (!isset($member)) {
            my_log("Deposit Received for user ID: {$user->id} {$user->name}", "System couldn't save. Please enter manually\nDeposited Amount: £{$amount}");
            logger("Deposit Received for user ID: {$user->id} {$user->name} System couldn't save. Please enter manually\nDeposited Amount: £{$amount}");
        }else{
            $member->deposit($amount, $request->payment_ref, "Paypal deposit by member", false);
            if(isset($request->invoice_id))$member->payInvoice( $request->invoice_id);

            $member->payInvoices();

            try{
                Notification::route('mail', $member->email)->notify(new DepositNotification($amount));
            }catch(Exception $e){
                logger("Failed to send deposit email. ".$e);
            }


        }

        if ($request->expectsJson()) {
            return response()->json($member);
        }



        return redirect(route('members-area.deposits'))->with([
            'message'    => "Your deposit of £{$amount} was successfully received.",
            'alert-type' => 'success',
        ]);
    }

    public function guestPayment(Request $request){
        $user = Auth::user()->memberDetails;
        $amount = $request->input('amount', '');
        $invoice_id = $request->input('invoice_id', '');

        return view('pages.guestpayment', compact('user', 'amount', 'invoice_id'));
    }


    public function guestPaymentContainer(Request $request){
        $user = Auth::user()->memberDetails;
        $amount = $request->input('amount', '');
        $invoice_id = $request->input('invoice_id', '');
        return view('pages.guestpaymentcontainer', compact('user', 'amount', 'invoice_id'));
    }


    public function applicantPayment(Applicant $applicant, Request $request){
        $amount = $request->input('amount', '');
        $invoice_id = $request->input('invoice_id', '');
        $applicant_id = $request->input('applicant_id', '');
        return view('pages.applicantpayment', compact('applicant_id', 'amount', 'invoice_id'));
    }

    
    public function applicantPaymentContainer(Applicant $applicant, Request $request){
        $amount = $request->input('amount', '');
        $invoice_id = $request->input('invoice_id', '');
        $applicant_id = $request->input('applicant_id', '');
        return view('pages.applicantpaymentcontainer', compact('applicant_id', 'amount', 'invoice_id'));
    }



    public function index()
    {
        $deposits = Deposit::whereMemberId(Auth::user()->member_id)
            ->orderBy('created_at', 'desc')
            ->get();
        return view('member.deposits', compact('deposits'));
    }
    public function manual($member)
    {
        $member = Member::find($member);
        // dd($member);
        return view('vendor.voyager.wallets.read', compact('member'));
    }
    public function manually(Request $request)
    {
        $member = Member::find($request->id);

        $member->deposit($request->amount, now().Auth::user()->id, "Manual balance adjustment by admin", true);
        $member->payInvoices();
        $redirect = redirect()->route("voyager.members.index");
        return $redirect->with([
            'message'    => __('voyager::generic.successfully_updated')." Deposit. Please wait a minute for transaction to reflect.",
            'alert-type' => 'success',
        ]);
    }

}
