<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateConstitutionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('constitution')) {
            Schema::create('constitution', function (Blueprint $table) {
                $table->increments('id');
                $table->string('title');
                $table->text('content');
                $table->string('version')->default('1.0');
                $table->boolean('is_active')->default(true);
                $table->datetime('effective_date')->nullable();
                $table->timestamps();
                $table->softDeletes();
                
                // Indexes
                $table->index(['is_active', 'effective_date']);
                $table->index('version');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('constitution');
    }
}
