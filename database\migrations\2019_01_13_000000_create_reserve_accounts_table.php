<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReserveAccountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('reserve_accounts')) {
            Schema::create('reserve_accounts', function (Blueprint $table) {
                $table->increments('id');
                $table->string('account_name');
                $table->text('description')->nullable();
                $table->decimal('balance', 15, 2)->default(0);
                $table->string('currency', 3)->default('GBP');
                $table->boolean('is_active')->default(true);
                $table->timestamps();
                $table->softDeletes();
                
                // Indexes
                $table->index(['is_active', 'currency']);
                $table->index('account_name');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('reserve_accounts');
    }
}
