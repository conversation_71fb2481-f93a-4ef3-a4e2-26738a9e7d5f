<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdminNotificationMemberTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('admin_notification_member')) {
            Schema::create('admin_notification_member', function (Blueprint $table) {
                $table->increments('id');
                $table->integer('admin_notification_id');
                $table->string('member_id');
                $table->boolean('is_read')->default(false);
                $table->datetime('read_at')->nullable();
                $table->timestamps();
                
                // Foreign key constraints
                $table->foreign('admin_notification_id', 'admin_notif_member_notif_id_foreign')
                      ->references('id')->on('admin_notifications')->onDelete('cascade');
                $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
                
                // Unique constraint to prevent duplicates
                $table->unique(['admin_notification_id', 'member_id'], 'admin_notif_member_unique');
                
                // Indexes
                $table->index('admin_notification_id');
                $table->index('member_id');
                $table->index(['is_read', 'read_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_notification_member');
    }
}
