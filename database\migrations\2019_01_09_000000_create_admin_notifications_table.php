<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAdminNotificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('admin_notifications')) {
            Schema::create('admin_notifications', function (Blueprint $table) {
                $table->increments('id');
                $table->string('title');
                $table->text('message');
                $table->string('type')->default('info'); // info, warning, error, success
                $table->boolean('is_read')->default(false);
                $table->string('action_url')->nullable();
                $table->timestamps();
                
                // Indexes
                $table->index(['is_read', 'created_at']);
                $table->index('type');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('admin_notifications');
    }
}
