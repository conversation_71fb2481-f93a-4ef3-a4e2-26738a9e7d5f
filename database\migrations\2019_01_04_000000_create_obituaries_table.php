<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateObituariesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('obituaries')) {
            Schema::create('obituaries', function (Blueprint $table) {
                $table->increments('id');
                $table->softDeletes();
                $table->timestamps();
                $table->string('full_name')->nullable();
                $table->text('biography')->nullable();
                $table->string('dob', 50)->nullable();
                $table->string('phone', 50)->nullable();
                $table->string('photo', 200)->nullable();
                $table->string('donated_amount', 64)->default('0')->nullable();
                $table->string('member_type', 32)->nullable();
                $table->integer('nominee_id')->nullable();
                $table->string('death_place', 200);
                $table->string('goal_amount', 64)->default('5000');
                $table->text('funeral_info')->nullable();
                $table->string('dod', 50)->nullable();
                $table->string('member_id')->nullable();
                
                // Foreign key constraints
                $table->foreign('member_id')->references('id')->on('members')->onDelete('set null');
                $table->foreign('nominee_id')->references('id')->on('nominees')->onDelete('set null');
                
                // Indexes
                $table->index('member_id');
                $table->index('nominee_id');
                $table->index('member_type');
                $table->index(['created_at', 'deleted_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('obituaries');
    }
}
